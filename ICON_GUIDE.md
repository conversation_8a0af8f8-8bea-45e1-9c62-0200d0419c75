# DXT 扩展图标指南

为了让您的 Confluence MCP 扩展在用户界面中更加美观和专业，建议添加一个图标。

## 图标要求

- **格式**: PNG
- **尺寸**: 建议 64x64 像素或 128x128 像素
- **文件名**: `icon.png`
- **位置**: 项目根目录

## 设计建议

1. **主题**: 与 Confluence 相关的设计元素
   - 可以使用 Confluence 的蓝色主题色 (#0052CC)
   - 包含文档、页面或知识库相关的图标元素

2. **风格**: 
   - 简洁明了
   - 在小尺寸下仍然清晰可见
   - 与现代 UI 设计风格一致

3. **内容建议**:
   - 文档图标 + Confluence 标识
   - 书本或页面图标
   - 齿轮图标（表示工具/服务）

## 添加图标

1. 将图标文件命名为 `icon.png` 并放在项目根目录
2. 图标会自动包含在 DXT 构建过程中
3. 在 `manifest.json` 中已经配置了图标路径

## 示例图标创建工具

- **在线工具**: 
  - Canva
  - Figma
  - Adobe Express

- **桌面软件**:
  - Adobe Illustrator
  - Sketch
  - GIMP (免费)

## 注意事项

- 确保您有权使用图标中的任何设计元素
- 避免使用受版权保护的 Atlassian/Confluence 官方标识
- 图标应该在浅色和深色背景下都清晰可见

如果您没有图标，扩展仍然可以正常工作，只是在用户界面中会显示默认图标。
