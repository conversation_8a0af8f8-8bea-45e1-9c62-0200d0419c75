# 构建 DXT 扩展指南

本文档说明如何将 Confluence MCP 项目打包成 DXT (Desktop Extension) 格式，以便在支持 MCP 的桌面应用程序中一键安装。

## 什么是 DXT？

DXT (Desktop Extensions) 是由 Anthropic 开发的一种打包格式，用于简化本地 MCP 服务器的分发和安装。它类似于 Chrome 扩展 (.crx) 或 VS Code 扩展 (.vsix)，让用户可以通过单击安装本地 MCP 服务器。

## 前置要求

1. **Node.js** (>= 16.0.0)
2. **Bun** (用于构建项目)
3. **已构建的项目**：确保运行过 `bun run build`

## 构建步骤

### 1. 安装 DXT CLI 工具（可选）

```bash
npm install -g @anthropic-ai/dxt
```

### 2. 使用项目内置脚本构建

```bash
# 构建项目并创建 DXT 文件
npm run build:dxt
```

这个命令会：
- 构建 TypeScript 项目到 `dist/` 目录
- 创建临时构建目录 `dxt-build/`
- 复制必要的文件（manifest.json, dist/, package.json 等）
- 安装生产依赖
- 创建 ZIP 压缩包并重命名为 `.dxt`
- 清理临时文件

### 3. 手动构建（如果自动脚本失败）

如果自动构建脚本失败，您可以手动创建 DXT 文件：

```bash
# 1. 确保项目已构建
bun run build

# 2. 创建构建目录
mkdir dxt-build
cd dxt-build

# 3. 复制必要文件
cp ../manifest.json .
cp ../package.json .
cp ../README.md .
cp ../LICENCE .
cp -r ../dist .

# 4. 安装生产依赖
npm install --production --no-optional

# 5. 创建 ZIP 文件
# Windows (PowerShell):
Compress-Archive -Path * -DestinationPath ../confluence-mcp.dxt

# macOS/Linux:
zip -r ../confluence-mcp.dxt .

# 6. 清理
cd ..
rm -rf dxt-build
```

## 文件结构

构建完成后，DXT 文件包含以下结构：

```
confluence-mcp.dxt (ZIP 文件)
├── manifest.json          # DXT 扩展清单
├── package.json           # Node.js 包信息
├── README.md              # 项目说明
├── LICENCE                # 许可证
├── dist/                  # 编译后的 JavaScript 文件
│   ├── index.js           # 主入口点
│   ├── index-multi.js     # 多传输入口点
│   └── ...                # 其他编译文件
└── node_modules/          # 生产依赖
    └── ...
```

## 测试 DXT 扩展

### 在 Claude Desktop 中测试

1. 打开 Claude Desktop
2. 双击 `confluence-mcp.dxt` 文件
3. 按照安装向导配置：
   - **Confluence API Token**: 您的个人访问令牌
   - **Confluence Base URL**: 您的 Confluence 实例 URL

### 验证安装

安装后，您应该能够在 Claude Desktop 中使用以下工具：
- `get_page` - 获取页面内容
- `search_pages` - 搜索页面
- `create_page` - 创建新页面
- `update_page` - 更新页面
- 等等...

## 分发

构建完成的 `confluence-mcp.dxt` 文件可以：
1. 直接分享给用户进行安装
2. 上传到扩展商店（如果有的话）
3. 通过 GitHub Releases 分发

## 故障排除

### 构建失败

1. **依赖问题**：确保运行 `bun install` 安装所有依赖
2. **构建问题**：确保 `bun run build` 成功完成
3. **权限问题**：确保脚本有执行权限

### 安装失败

1. **清单验证**：检查 `manifest.json` 格式是否正确
2. **依赖缺失**：确保所有生产依赖都包含在 DXT 文件中
3. **平台兼容性**：检查目标平台是否在 `compatibility` 中列出

## 更新扩展

要更新扩展：
1. 更新 `manifest.json` 中的版本号
2. 重新构建 DXT 文件
3. 用户需要重新安装新版本

## 相关资源

- [DXT 官方文档](https://github.com/anthropics/dxt)
- [MCP 协议文档](https://modelcontextprotocol.io/)
- [Confluence REST API](https://developer.atlassian.com/cloud/confluence/rest/v2/)
