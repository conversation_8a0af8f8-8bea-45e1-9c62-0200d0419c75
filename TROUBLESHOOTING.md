# MCP 连接问题排查指南

## 🚨 常见错误：Connection closed

### 错误信息
```
Failed to connect: McpError: MCP error -32000: Connection closed
```

### 🔍 原因分析

这个错误通常由以下原因引起：

1. **缺少环境变量**（最常见）
2. **API Token 无效**
3. **Base URL 格式错误**
4. **网络连接问题**
5. **服务器启动失败**

## 🛠️ 解决步骤

### 步骤 1：运行诊断工具

```bash
npm run diagnose
```

这个工具会检查：
- 构建文件是否存在
- 环境变量是否设置
- MCP 服务器是否能正常启动

### 步骤 2：设置环境变量

#### 方法 A：创建 .env 文件（推荐）

```bash
# 复制示例文件
cp .env.example .env

# 编辑 .env 文件
# 填入您的实际配置
```

`.env` 文件内容示例：
```env
CONFLUENCE_API_TOKEN=your_actual_api_token_here
CONFLUENCE_BASE_URL=https://your-domain.atlassian.net/wiki
MCP_TRANSPORT=stdio
```

#### 方法 B：直接设置环境变量

```bash
# Windows (PowerShell)
$env:CONFLUENCE_API_TOKEN="your_token"
$env:CONFLUENCE_BASE_URL="https://your-domain.atlassian.net/wiki"

# macOS/Linux
export CONFLUENCE_API_TOKEN="your_token"
export CONFLUENCE_BASE_URL="https://your-domain.atlassian.net/wiki"
```

### 步骤 3：获取 Confluence API Token

1. 访问 [Atlassian API Tokens](https://id.atlassian.com/manage/api-tokens)
2. 点击"Create API token"
3. 输入标签名称（如 "MCP Server"）
4. 复制生成的 token
5. **重要**：妥善保存，token 只显示一次

### 步骤 4：验证 Base URL 格式

确保 URL 格式正确：
```
✅ 正确：https://your-domain.atlassian.net/wiki
❌ 错误：https://your-domain.atlassian.net
❌ 错误：https://your-domain.atlassian.net/
❌ 错误：your-domain.atlassian.net/wiki
```

### 步骤 5：测试连接

```bash
# 测试 MCP 服务器
bun dist/index.js

# 应该看到：
# Confluence MCP server running on stdio
```

## 🔧 Claude Desktop 配置

如果在 Claude Desktop 中使用，确保配置正确：

### 使用 DXT 扩展（推荐）

1. 重新构建 DXT：`npm run build:dxt`
2. 双击 `confluence-mcp.dxt` 安装
3. 在安装向导中输入正确的配置

### 手动 JSON 配置

```json
{
  "mcpServers": {
    "confluence": {
      "command": "bun",
      "args": ["/absolute/path/to/confluence-mcp/dist/index.js"],
      "env": {
        "CONFLUENCE_API_TOKEN": "your_api_token",
        "CONFLUENCE_BASE_URL": "https://your-domain.atlassian.net/wiki"
      }
    }
  }
}
```

## 🧪 验证修复

### 1. 运行诊断
```bash
npm run diagnose
```

应该看到：
```
✅ 构建文件存在
✅ 环境变量完整
✅ MCP 服务器启动成功
✅ 初始化响应正常
```

### 2. 测试工具
```bash
# 在 Claude Desktop 中测试
"请列出我的 Confluence 空间"
```

## 🔍 进一步排查

如果问题仍然存在：

### 检查网络连接
```bash
# 测试 Confluence 连接
curl -H "Authorization: Bearer your_token" \
     "https://your-domain.atlassian.net/wiki/rest/api/space"
```

### 查看详细日志
```bash
# 启动时显示详细输出
DEBUG=* bun dist/index.js
```

### 重新构建
```bash
# 清理并重新构建
rm -rf dist/
npm run build
npm run build:dxt
```

## 📞 获取帮助

如果问题仍未解决：

1. 查看 [项目 Issues](https://github.com/scutken/confluence-mcp/issues)
2. 提交新的 Issue，包含：
   - 错误信息
   - 诊断工具输出
   - 操作系统信息
   - Claude Desktop 版本

## ✅ 成功标志

当一切正常时，您应该能够：
- 在 Claude Desktop 中看到 Confluence 工具
- 成功搜索 Confluence 页面
- 创建和更新页面
- 管理评论和附件
