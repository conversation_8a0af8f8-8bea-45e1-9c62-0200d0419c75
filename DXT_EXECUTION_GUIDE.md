# DXT 文件执行指南

## 🎯 DXT 文件执行方式

DXT 文件本身不是可执行文件，而是一个包含 MCP 服务器和配置的压缩包。它需要通过支持 DXT 格式的应用程序来"执行"。

## 🖥️ 在支持的应用中执行

### 1. <PERSON>ktop（推荐）

Claude Desktop 是目前主要支持 DXT 格式的应用：

```bash
# 安装步骤
1. 双击 confluence-mcp.dxt 文件
2. Claude Desktop 自动打开安装向导
3. 配置必要参数：
   - Confluence API Token
   - Confluence Base URL
4. 点击"安装"完成
```

**执行原理**：
- Claude Desktop 解压 DXT 到本地目录
- 读取 `manifest.json` 获取配置
- 根据 `mcp_config` 启动 Node.js 进程
- 建立 stdio 通信管道

### 2. 其他 MCP 客户端

任何实现了 DXT 支持的 MCP 客户端都可以执行，但目前主要是 Claude Desktop。

## 🔧 手动执行（开发/测试）

### 方法 1：使用测试工具

```bash
# 交互式测试 DXT 内容
npm run test:dxt:manual

# 这个工具会：
# 1. 解压 DXT 文件
# 2. 显示配置信息
# 3. 提供手动启动选项
```

### 方法 2：手动解压和执行

```bash
# 1. 解压 DXT 文件
unzip confluence-mcp.dxt -d temp-dxt/
cd temp-dxt/

# 2. 设置环境变量
export CONFLUENCE_API_TOKEN="your_api_token"
export CONFLUENCE_BASE_URL="https://your-domain.atlassian.net/wiki"

# 3. 启动 MCP 服务器
node dist/index.js
```

### 方法 3：使用 DXT CLI 工具

```bash
# 安装 DXT CLI
npm install -g @anthropic-ai/dxt

# 运行 DXT 文件（如果支持）
dxt run confluence-mcp.dxt
```

## 📋 DXT 内部结构

当应用程序"执行" DXT 文件时，实际上是：

```
confluence-mcp.dxt (ZIP 文件)
├── manifest.json          # 扩展配置
├── dist/
│   ├── index.js           # 主入口点 ← 实际执行的文件
│   └── ...
├── node_modules/          # 依赖包
└── package.json
```

**执行流程**：
1. 应用读取 `manifest.json`
2. 根据 `server.mcp_config` 配置启动进程
3. 执行命令：`node dist/index.js`
4. 建立 MCP 通信连接

## ⚙️ 配置变量替换

DXT 应用会自动替换配置中的变量：

```json
{
  "mcp_config": {
    "command": "node",
    "args": ["${__dirname}/dist/index.js"],
    "env": {
      "CONFLUENCE_API_TOKEN": "${user_config.api_token}",
      "CONFLUENCE_BASE_URL": "${user_config.base_url}"
    }
  }
}
```

**变量替换**：
- `${__dirname}` → DXT 解压后的目录路径
- `${user_config.api_token}` → 用户配置的 API Token
- `${user_config.base_url}` → 用户配置的 Base URL

## 🧪 测试 DXT 执行

### 验证 DXT 结构

```bash
npm run test:dxt
```

### 手动测试执行

```bash
npm run test:dxt:manual
```

这个工具会：
- 解压 DXT 文件
- 显示配置信息
- 提供交互式启动选项
- 模拟应用程序的执行过程

### 直接测试 MCP 服务器

```bash
# 设置环境变量
export CONFLUENCE_API_TOKEN="your_token"
export CONFLUENCE_BASE_URL="https://your-domain.atlassian.net/wiki"

# 直接运行构建后的服务器
bun dist/index.js
```

## 🔍 调试 DXT 执行

### 查看日志

DXT 应用通常会提供日志查看功能：

1. **Claude Desktop**：
   - 查看应用日志
   - 检查 MCP 服务器状态

2. **手动调试**：
   ```bash
   # 启动时显示详细输出
   DEBUG=* node dist/index.js
   ```

### 常见问题

1. **DXT 无法安装**
   - 检查文件完整性
   - 验证应用版本兼容性

2. **服务器启动失败**
   - 检查 Node.js 版本
   - 验证依赖完整性
   - 确认环境变量设置

3. **工具无法使用**
   - 检查网络连接
   - 验证 API Token 权限
   - 确认 Confluence URL 正确

## 📚 相关资源

- [DXT 官方文档](https://github.com/anthropics/dxt)
- [MCP 协议文档](https://modelcontextprotocol.io/)
- [Claude Desktop 文档](https://claude.ai/desktop)

## 💡 开发建议

1. **本地测试**：使用 `npm run test:dxt:manual` 验证功能
2. **用户测试**：在 Claude Desktop 中安装测试
3. **错误处理**：确保服务器有良好的错误处理
4. **日志记录**：添加适当的日志输出便于调试
