# Confluence MCP DXT 扩展安装指南

## 🎉 恭喜！DXT 扩展构建成功

您的 Confluence MCP 项目已经成功打包成 DXT 扩展格式！文件名为：`confluence-mcp.dxt`

## 📦 文件信息

- **文件名**: `confluence-mcp.dxt`
- **文件大小**: 约 3.83 MB
- **格式**: DXT (Desktop Extension)
- **兼容性**: Claude Desktop, 其他支持 MCP 的桌面应用

## 🚀 安装步骤

### 在 Claude Desktop 中安装

1. **下载扩展文件**
   - 确保您有 `confluence-mcp.dxt` 文件

2. **安装扩展**
   - 双击 `confluence-mcp.dxt` 文件
   - Claude Desktop 会自动打开安装向导

3. **配置扩展**
   - **Confluence API Token**: 输入您的个人访问令牌
     - 获取方式：Confluence → 用户设置 → 个人访问令牌
   - **Confluence Base URL**: 输入您的 Confluence 实例 URL
     - 格式：`https://your-domain.atlassian.net/wiki`

4. **完成安装**
   - 点击"安装"按钮
   - 等待安装完成

## ✅ 验证安装

安装完成后，您可以在 Claude Desktop 中使用以下工具：

### 可用工具

1. **get_page** - 获取页面内容
2. **search_pages** - 搜索页面
3. **get_spaces** - 获取空间列表
4. **create_page** - 创建新页面
5. **update_page** - 更新页面
6. **get_comments** - 获取评论
7. **add_comment** - 添加评论
8. **get_attachments** - 获取附件
9. **add_attachment** - 添加附件

### 测试示例

尝试以下命令来测试扩展是否正常工作：

```
请帮我搜索 Confluence 中包含"文档"关键词的页面
```

```
请获取我的 Confluence 空间列表
```

## 🔧 故障排除

### 常见问题

1. **安装失败**
   - 确保使用的是最新版本的 Claude Desktop
   - 检查 DXT 文件是否完整下载

2. **配置错误**
   - 验证 API Token 是否正确
   - 确认 Base URL 格式正确（必须包含 `/wiki`）

3. **工具无法使用**
   - 检查网络连接
   - 验证 Confluence 实例是否可访问
   - 确认 API Token 权限足够

### 获取帮助

如果遇到问题，请：
1. 查看 [项目文档](README.md)
2. 提交 [Issue](https://github.com/scutken/confluence-mcp/issues)
3. 查看 [DXT 官方文档](https://github.com/anthropics/dxt)

## 📤 分享扩展

您可以将 `confluence-mcp.dxt` 文件分享给其他用户：

1. **直接分享文件**
   - 通过邮件、云盘等方式分享 DXT 文件
   - 用户双击即可安装

2. **GitHub Releases**
   - 上传到 GitHub Releases 页面
   - 用户可以直接下载安装

3. **企业内部分发**
   - 放置在内部文件服务器
   - 通过企业软件分发系统部署

## 🔄 更新扩展

当有新版本时：
1. 重新构建 DXT 文件：`npm run build:dxt`
2. 用户需要重新安装新版本的 DXT 文件
3. 旧版本会被自动替换

## 🎯 下一步

- 收集用户反馈
- 优化工具功能
- 添加更多 Confluence 集成特性
- 考虑发布到扩展商店（如果有的话）

---

**恭喜您成功创建了第一个 DXT 扩展！** 🎉
